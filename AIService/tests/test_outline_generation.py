#!/usr/bin/env python3
"""
Test script for Outline Generation Service

This script tests the improved outline generation functionality
that uses actual content and structure compliance data for better
government proposal outline generation.
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from AIService
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent.parent / ".env")
except ImportError:
    print("python-dotenv not available. Please run from virtual environment or install dependencies.")
    print("To run with virtual environment:")
    print("  source venv/bin/activate  # or venv\\Scripts\\activate on Windows")
    print("  python tests/test_outline_generation.py")
    sys.exit(1)

from services.proposal.outline import ProposalOutlineService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService

try:
    from loguru import logger
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
except ImportError:
    print("loguru not available. Please run from virtual environment or install dependencies.")
    print("To run with virtual environment:")
    print("  source venv/bin/activate  # or venv\\Scripts\\activate on Windows")
    print("  python tests/test_outline_generation.py")
    sys.exit(1)

# Verify environment is loaded
logger.info(f"LLM Provider: {os.getenv('LLM_PROVIDER', 'Not set')}")
logger.info(f"LLM Model: {os.getenv('LLM_MODEL', 'Not set')}")
logger.info(f"Gemini API Key loaded: {'Yes' if os.getenv('GEMINI_API_KEY') else 'No'}")

def validate_outline_structure(outline_content):
    """
    Validate the generated outline against government proposal standards.
    """
    
    logger.info("Validating outline structure and content...")
    
    try:
        # Parse JSON if it's a string
        if isinstance(outline_content, str):
            # Remove markdown code block formatting if present
            if outline_content.startswith("```json"):
                outline_content = outline_content.replace("```json", "").replace("```", "").strip()
            outline_data = json.loads(outline_content)
        else:
            outline_data = outline_content
        
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "statistics": {},
            "sections": []
        }
        
        # Check for required top-level structure
        if "outlines" not in outline_data:
            validation_results["errors"].append("Missing 'outlines' key")
            validation_results["is_valid"] = False
            return validation_results
        
        outlines = outline_data["outlines"]
        validation_results["statistics"]["total_outlines"] = len(outlines)
        
        # Validate each outline
        required_fields = ["title", "content", "page_limit", "purpose", "custom_prompt"]
        
        for i, outline in enumerate(outlines):
            outline_info = {
                "index": i + 1,
                "title": outline.get("title", "Unknown"),
                "content_length": len(outline.get("content", "")),
                "custom_prompt_length": len(outline.get("custom_prompt", "")),
                "page_limit": outline.get("page_limit", 0),
                "purpose": outline.get("purpose", "Unknown"),
                "issues": []
            }
            
            # Validate required fields
            for field in required_fields:
                if field not in outline or not outline[field]:
                    outline_info["issues"].append(f"Missing or empty '{field}' field")
                    validation_results["errors"].append(f"Outline {i+1}: Missing '{field}'")
                    validation_results["is_valid"] = False
            
            # Validate content length
            content = outline.get("content", "")
            if len(content) < 200:
                outline_info["issues"].append(f"Content too short ({len(content)} chars, min 200)")
                validation_results["warnings"].append(f"Outline '{outline.get('title', 'Unknown')}': Content too short")
            
            # Check for placeholders
            if any(placeholder in content.lower() for placeholder in ["[", "]", "tbd", "todo", "placeholder"]):
                outline_info["issues"].append("Content contains placeholders")
                validation_results["errors"].append(f"Outline '{outline.get('title', 'Unknown')}': Contains placeholders")
                validation_results["is_valid"] = False
            
            # Validate custom prompt
            custom_prompt = outline.get("custom_prompt", "")
            if len(custom_prompt) < 100:
                outline_info["issues"].append(f"Custom prompt too short ({len(custom_prompt)} chars, min 100)")
                validation_results["warnings"].append(f"Outline '{outline.get('title', 'Unknown')}': Custom prompt too short")
            
            # Validate page limit
            page_limit = outline.get("page_limit", 0)
            if page_limit <= 0 or page_limit > 20:
                outline_info["issues"].append(f"Invalid page limit: {page_limit}")
                validation_results["warnings"].append(f"Outline '{outline.get('title', 'Unknown')}': Invalid page limit")
            
            validation_results["sections"].append(outline_info)
        
        # Calculate statistics
        validation_results["statistics"].update({
            "outlines_with_issues": sum(1 for s in validation_results["sections"] if s["issues"]),
            "average_content_length": sum(s["content_length"] for s in validation_results["sections"]) / len(validation_results["sections"]) if validation_results["sections"] else 0,
            "average_page_limit": sum(s["page_limit"] for s in validation_results["sections"]) / len(validation_results["sections"]) if validation_results["sections"] else 0,
            "error_count": len(validation_results["errors"]),
            "warning_count": len(validation_results["warnings"])
        })
        
        # Overall validation result
        if validation_results["errors"]:
            validation_results["is_valid"] = False
            logger.error(f"❌ Outline validation failed with {len(validation_results['errors'])} errors")
        else:
            logger.success(f"✅ Outline validation passed with {len(validation_results['warnings'])} warnings")
        
        return validation_results
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON structure: {e}")
        return {
            "is_valid": False,
            "errors": [f"JSON parsing error: {e}"],
            "warnings": [],
            "statistics": {},
            "sections": []
        }
    except Exception as e:
        logger.error(f"Error validating outline: {e}")
        return {
            "is_valid": False,
            "errors": [f"Validation error: {e}"],
            "warnings": [],
            "statistics": {},
            "sections": []
        }

async def test_outline_generation():
    """Test the outline generation service with actual compliance data"""
    
    # Initialize the services
    logger.info("Initializing services...")
    outline_service = ProposalOutlineService()
    content_service = ContentComplianceService()
    structure_service = StructureComplianceService()
    
    # Test parameters
    test_opportunity_id = "vSe1unlCj9"
    test_tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    test_source = "custom"
    
    logger.info(f"Testing with opportunity_id={test_opportunity_id}, tenant_id={test_tenant_id}, source={test_source}")
    
    try:
        # Step 1: Generate structure compliance
        logger.info("Step 1: Generating structure compliance...")
        structure_result = await structure_service.generate_structure_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            max_tokens=3096
        )
        structure_compliance = structure_result["content"]
        logger.info("✅ Structure compliance generated successfully")
        
        # Step 2: Generate content compliance
        logger.info("Step 2: Generating content compliance...")
        content_result = await content_service.generate_content_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            is_rfp=True
        )
        content_compliance = content_result["content"]
        logger.info("✅ Content compliance generated successfully")
        
        # Step 3: Generate table of contents
        logger.info("Step 3: Generating table of contents...")
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            volume_information=structure_compliance,
            content_compliance=content_compliance,
            is_rfp=True
        )
        
        # Parse table of contents
        toc_content = toc_result["content"]
        if toc_content.startswith("```json"):
            toc_content = toc_content.replace("```json", "").replace("```", "").strip()
        toc_data = json.loads(toc_content)
        table_of_contents = toc_data["table_of_contents"]
        logger.info(f"✅ Table of contents generated with {len(table_of_contents)} sections")
        
        # Step 4: Generate outline with compliance data
        logger.info("Step 4: Generating outline with compliance data...")
        outline_result = await outline_service.generate_outline(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            table_of_contents=table_of_contents,
            is_rfp=True
        )

        logger.info("✅ Outline generated successfully")

        # Extract outline data and generation summary
        outlines = outline_result.get("outlines", [])
        generation_summary = outline_result.get("generation_summary", {})

        logger.info(f"Generated {len(outlines)} outlines")
        logger.info(f"Success rate: {generation_summary.get('success_rate', 0):.1f}%")
        
        # Show the compliance data that was used
        logger.info("\n" + "=" * 80)
        logger.info("COMPLIANCE DATA USED")
        logger.info("=" * 80)
        logger.info("Structure Compliance:")
        logger.info("-" * 40)
        logger.info(structure_compliance[:300] + "..." if len(structure_compliance) > 300 else structure_compliance)
        logger.info("\nContent Compliance:")
        logger.info("-" * 40)
        logger.info(content_compliance[:300] + "..." if len(content_compliance) > 300 else content_compliance)
        
        logger.info("\n" + "=" * 80)
        logger.info("GENERATED OUTLINE")
        logger.info("=" * 80)

        # Convert outline result to JSON string for display
        outline_json = json.dumps(outline_result, indent=2)
        logger.info(outline_json[:1000] + "..." if len(outline_json) > 1000 else outline_json)
        logger.info("=" * 80)
        
        # Validate the generated outline
        logger.info("\nValidating outline...")
        outline_validation = validate_outline_structure(outline_result)
        
        if outline_validation["is_valid"]:
            logger.success("✅ Outline validation passed!")
        else:
            logger.error("❌ Outline validation failed!")
            for error in outline_validation["errors"]:
                logger.error(f"  - {error}")
        
        # Show validation statistics
        stats = outline_validation["statistics"]
        logger.info(f"\nValidation Statistics:")
        logger.info(f"  Total outlines: {stats.get('total_outlines', 0)}")
        logger.info(f"  Outlines with issues: {stats.get('outlines_with_issues', 0)}")
        logger.info(f"  Average content length: {stats.get('average_content_length', 0):.1f} chars")
        logger.info(f"  Average page limit: {stats.get('average_page_limit', 0):.1f} pages")
        logger.info(f"  Errors: {stats.get('error_count', 0)}")
        logger.info(f"  Warnings: {stats.get('warning_count', 0)}")
        
        # Save results to files for review
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        
        # Save all generated content
        with open(output_dir / "outline_generation.json", "w") as f:
            json.dump(outline_result, f, indent=2)
        
        with open(output_dir / "outline_validation.json", "w") as f:
            json.dump(outline_validation, f, indent=2)
        
        with open(output_dir / "outline_structure_compliance.json", "w") as f:
            f.write(structure_compliance)
            
        with open(output_dir / "outline_content_compliance.txt", "w") as f:
            f.write(content_compliance)
            
        with open(output_dir / "outline_table_of_contents.json", "w") as f:
            f.write(toc_result["content"])
        
        # Save comprehensive test report
        test_report = {
            "test_parameters": {
                "opportunity_id": test_opportunity_id,
                "tenant_id": test_tenant_id,
                "source": test_source,
                "is_rfp": True
            },
            "compliance_data_lengths": {
                "structure_compliance": len(structure_compliance),
                "content_compliance": len(content_compliance)
            },
            "table_of_contents_sections": len(table_of_contents),
            "validation_results": outline_validation,
            "generated_outlines_count": len(outlines),
            "generation_summary": generation_summary,
            "test_timestamp": str(asyncio.get_event_loop().time())
        }
        
        with open(output_dir / "outline_test_report.json", "w") as f:
            json.dump(test_report, f, indent=2)
        
        logger.info(f"Results saved to {output_dir} directory")
        logger.success("Outline generation testing completed successfully!")
        
        return outline_validation["is_valid"]
        
    except Exception as e:
        logger.error(f"Error during outline generation testing: {e}")
        raise

def main():
    """Main function to run the tests"""
    
    logger.info("Starting Outline Generation Tests")
    logger.info("=" * 60)
    
    try:
        # Run the main test
        success = asyncio.run(test_outline_generation())
        
        if success:
            logger.success("🎉 All outline generation tests passed!")
            sys.exit(0)
        else:
            logger.error("❌ Outline generation tests failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
