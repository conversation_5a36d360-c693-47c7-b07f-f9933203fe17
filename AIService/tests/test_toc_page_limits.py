#!/usr/bin/env python3
"""
Test to verify table of contents generation includes page limits
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from AIService
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent.parent / ".env")
except ImportError:
    print("python-dotenv not available. Please run from virtual environment.")
    sys.exit(1)

from services.proposal.outline import ProposalOutlineService
from services.proposal.structure_compliance import StructureComplianceService

try:
    from loguru import logger
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
except ImportError:
    print("loguru not available. Please run from virtual environment.")
    sys.exit(1)

async def test_toc_page_limits():
    """Test that table of contents generation includes page limits for all sections"""
    
    # Initialize the services
    logger.info("Initializing services...")
    outline_service = ProposalOutlineService()
    structure_service = StructureComplianceService()
    
    # Test parameters
    test_opportunity_id = "vSe1unlCj9"
    test_tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    test_source = "custom"
    
    logger.info(f"Testing with opportunity_id={test_opportunity_id}")
    
    try:
        # Step 1: Generate structure compliance
        logger.info("Step 1: Generating structure compliance...")
        structure_result = await structure_service.generate_structure_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            max_tokens=3096
        )
        structure_compliance = structure_result["content"]
        logger.info("✅ Structure compliance generated")
        
        # Step 2: Generate table of contents
        logger.info("Step 2: Generating table of contents...")
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            volume_information=structure_compliance,
            content_compliance="",  # Not needed for this test
            is_rfp=True
        )
        
        # Parse table of contents
        toc_content = toc_result["content"]
        if toc_content.startswith("```json"):
            toc_content = toc_content.replace("```json", "").replace("```", "").strip()
        toc_data = json.loads(toc_content)
        table_of_contents = toc_data["table_of_contents"]
        
        logger.info(f"✅ Table of contents generated with {len(table_of_contents)} sections")
        
        # Parse structure compliance to get expected page limits
        structure_data = json.loads(structure_compliance.replace("```json", "").replace("```", "").strip())
        expected_page_limits = {}
        for volume in structure_data["structure"]:
            for section in volume["content"]:
                section_name = section.get("section_name", "")
                page_limit = section.get("page_limit", 1)
                expected_page_limits[section_name] = page_limit
        
        logger.info(f"Expected page limits from structure compliance: {expected_page_limits}")
        
        # Verify that all sections have page_limit field
        success = True
        for section in table_of_contents:
            section_title = section.get("title", "")
            section_page_limit = section.get("page_limit")
            
            # Check if page_limit field exists
            if section_page_limit is None:
                logger.error(f"❌ MISSING PAGE LIMIT: Section '{section_title}' has no page_limit field")
                success = False
            else:
                logger.info(f"✅ PAGE LIMIT FOUND: '{section_title}' = {section_page_limit} pages")
                
                # Check if page limit matches expected value
                expected_limit = expected_page_limits.get(section_title)
                if expected_limit and section_page_limit != expected_limit:
                    logger.warning(f"⚠️  PAGE LIMIT MISMATCH: '{section_title}' expected {expected_limit}, got {section_page_limit}")
                elif expected_limit:
                    logger.info(f"✅ PAGE LIMIT CORRECT: '{section_title}' = {section_page_limit} pages")
            
            # Check subsections
            subsections = section.get("subsections", [])
            for subsection in subsections:
                subsection_title = subsection.get("title", "")
                subsection_page_limit = subsection.get("page_limit")
                
                if subsection_page_limit is None:
                    logger.error(f"❌ MISSING PAGE LIMIT: Subsection '{subsection_title}' has no page_limit field")
                    success = False
                else:
                    logger.info(f"✅ PAGE LIMIT FOUND: Subsection '{subsection_title}' = {subsection_page_limit} pages")
        
        # Save results for inspection
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "toc_page_limits_test.json", "w") as f:
            json.dump({
                "table_of_contents": table_of_contents,
                "expected_page_limits": expected_page_limits,
                "structure_compliance": structure_compliance
            }, f, indent=2)
        
        logger.info(f"✅ Results saved to {output_dir}/toc_page_limits_test.json")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error during TOC page limits testing: {e}")
        raise

def main():
    """Main function to run the TOC page limits test"""
    
    logger.info("Starting TOC Page Limits Test")
    logger.info("=" * 50)
    
    try:
        success = asyncio.run(test_toc_page_limits())
        
        if success:
            logger.success("🎉 TOC page limits test passed!")
            sys.exit(0)
        else:
            logger.error("❌ TOC page limits test failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
