#!/usr/bin/env python3
"""
Simple test to verify that the validation fix works for page limit patterns
"""

def test_page_limit_patterns():
    """Test that different page limit patterns are recognized"""
    
    def check_page_limit_mentioned(expected_page_limit, prompt):
        """Replicate the validation logic"""
        page_limit_mentioned = False
        page_limit_patterns = [
            str(expected_page_limit) + " page",  # "1 page" or "2 pages"
            str(expected_page_limit) + "-page",  # "1-page" or "2-page"
            f"{expected_page_limit} pages",      # "1 pages" or "2 pages"
        ]
        
        # Special cases for numbers written as words
        if expected_page_limit == 1:
            page_limit_patterns.extend(["one page", "one-page", "single page"])
        elif expected_page_limit == 2:
            page_limit_patterns.extend(["two page", "two-page"])
        elif expected_page_limit == 3:
            page_limit_patterns.extend(["three page", "three-page"])
        elif expected_page_limit == 4:
            page_limit_patterns.extend(["four page", "four-page"])
        
        for pattern in page_limit_patterns:
            if pattern.lower() in prompt.lower():
                page_limit_mentioned = True
                break
        
        return page_limit_mentioned
    
    # Test cases: (expected_page_limit, custom_prompt, should_pass)
    test_cases = [
        (1, "Write a one-page section", True),
        (1, "Write a 1-page section", True),
        (1, "Create a single page document", True),
        (1, "Generate 1 page of content", True),
        (1, "Write 1 pages of content", True),
        (2, "Write a two-page section", True),
        (2, "Create a 2-page document", True),
        (2, "Generate 2 pages of content", True),
        (4, "Create a four-page document", True),
        (4, "Generate 4 pages of content", True),
        (1, "Write a document with no page limit mentioned", False),
        (2, "Create content for 3 pages", False),  # Wrong number
    ]
    
    print("Testing page limit validation patterns...")
    print("="*60)
    
    all_passed = True
    for expected_limit, prompt, should_pass in test_cases:
        validation_passed = check_page_limit_mentioned(expected_limit, prompt)
        
        if validation_passed == should_pass:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
            all_passed = False
        
        print(f"{status}: Expected {expected_limit} pages")
        print(f"      Prompt: '{prompt}'")
        print(f"      Should pass: {should_pass}, Actually passed: {validation_passed}")
        print()
    
    print("="*60)
    if all_passed:
        print("🎉 ALL VALIDATION PATTERN TESTS PASSED!")
        print("The validation fix correctly recognizes different page limit patterns!")
        return True
    else:
        print("❌ SOME VALIDATION PATTERN TESTS FAILED!")
        return False

if __name__ == "__main__":
    success = test_page_limit_patterns()
    exit(0 if success else 1)
