#!/usr/bin/env python3
"""
Test to verify outline generation works with only table of contents (no compliance parameters)
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from AIService
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent.parent / ".env")
except ImportError:
    print("python-dotenv not available. Please run from virtual environment.")
    sys.exit(1)

from services.proposal.outline import ProposalOutlineService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService

try:
    from loguru import logger
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
except ImportError:
    print("loguru not available. Please run from virtual environment.")
    sys.exit(1)

async def test_outline_toc_only():
    """Test that outline generation works with only table of contents"""
    
    # Initialize the services
    logger.info("Initializing services...")
    outline_service = ProposalOutlineService()
    content_service = ContentComplianceService()
    structure_service = StructureComplianceService()
    
    # Test parameters
    test_opportunity_id = "vSe1unlCj9"
    test_tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    test_source = "custom"
    
    logger.info(f"Testing with opportunity_id={test_opportunity_id}")
    
    try:
        # Step 1: Generate structure compliance (for TOC generation)
        logger.info("Step 1: Generating structure compliance...")
        structure_result = await structure_service.generate_structure_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            max_tokens=3096
        )
        structure_compliance = structure_result["content"]
        logger.info("✅ Structure compliance generated")
        
        # Step 2: Generate content compliance (for TOC generation)
        logger.info("Step 2: Generating content compliance...")
        content_result = await content_service.generate_content_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            is_rfp=True
        )
        content_compliance = content_result["content"]
        logger.info("✅ Content compliance generated")
        
        # Step 3: Generate table of contents with page limits
        logger.info("Step 3: Generating table of contents...")
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            volume_information=structure_compliance,
            content_compliance=content_compliance,
            is_rfp=True
        )
        
        # Parse table of contents
        toc_content = toc_result["content"]
        if toc_content.startswith("```json"):
            toc_content = toc_content.replace("```json", "").replace("```", "").strip()
        toc_data = json.loads(toc_content)
        table_of_contents = toc_data["table_of_contents"]
        
        # Test with first 2 sections only for speed
        table_of_contents = table_of_contents[:2]
        logger.info(f"✅ Table of contents generated - testing with {len(table_of_contents)} sections")
        
        # Step 4: Generate outline using ONLY table of contents (no compliance parameters)
        logger.info("Step 4: Generating outline using ONLY table of contents...")
        outline_result = await outline_service.generate_outline(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            table_of_contents=table_of_contents,
            is_rfp=True
        )
        
        logger.info("✅ Outline generated successfully using only table of contents!")
        
        # Extract outline data
        outlines = outline_result.get("outlines", [])
        generation_summary = outline_result.get("generation_summary", {})
        
        logger.info(f"Generated {len(outlines)} outlines")
        logger.info(f"Success rate: {generation_summary.get('success_rate', 0):.1f}%")
        
        # Verify that outlines use correct titles and page limits from TOC
        success = True
        for i, outline in enumerate(outlines):
            toc_section = table_of_contents[i]
            expected_title = toc_section.get("title", "")
            expected_page_limit = toc_section.get("page_limit", 2)
            
            actual_title = outline.get("title", "")
            actual_page_limit = outline.get("page_limit", 0)
            
            # Check exact title match
            if actual_title != expected_title:
                logger.error(f"❌ TITLE MISMATCH: Expected '{expected_title}', got '{actual_title}'")
                success = False
            else:
                logger.info(f"✅ TITLE CORRECT: '{actual_title}'")
            
            # Check page limit
            if actual_page_limit != expected_page_limit:
                logger.error(f"❌ PAGE LIMIT MISMATCH: '{expected_title}' expected {expected_page_limit}, got {actual_page_limit}")
                success = False
            else:
                logger.info(f"✅ PAGE LIMIT CORRECT: '{expected_title}' = {actual_page_limit} pages")
            
            # Check that outline has substantial content
            content_length = len(outline.get("content", ""))
            if content_length < 200:
                logger.error(f"❌ CONTENT TOO SHORT: '{expected_title}' content is only {content_length} characters")
                success = False
            else:
                logger.info(f"✅ CONTENT ADEQUATE: '{expected_title}' content is {content_length} characters")
        
        # Save results for inspection
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "outline_toc_only_test.json", "w") as f:
            json.dump({
                "outlines": outlines,
                "table_of_contents": table_of_contents,
                "generation_summary": generation_summary
            }, f, indent=2)
        
        logger.info(f"✅ Results saved to {output_dir}/outline_toc_only_test.json")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error during outline TOC-only testing: {e}")
        raise

def main():
    """Main function to run the TOC-only test"""
    
    logger.info("Starting Outline TOC-Only Test")
    logger.info("=" * 50)
    
    try:
        success = asyncio.run(test_outline_toc_only())
        
        if success:
            logger.success("🎉 Outline TOC-only test passed!")
            sys.exit(0)
        else:
            logger.error("❌ Outline TOC-only test failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
