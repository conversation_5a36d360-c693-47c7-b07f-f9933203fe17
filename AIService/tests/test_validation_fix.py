#!/usr/bin/env python3
"""
Test to verify that the validation fix works for page limit patterns
"""

import sys
from pathlib import Path

# Add the parent directory to the path so we can import from AIService
sys.path.append(str(Path(__file__).parent.parent))

from services.proposal.outline import ProposalOutlineService

def test_validation_patterns():
    """Test that the validation function recognizes different page limit patterns"""
    
    service = ProposalOutlineService()
    
    # Test cases: (expected_page_limit, custom_prompt, should_pass)
    test_cases = [
        (1, "Write a one-page section", True),
        (1, "Write a 1-page section", True),
        (1, "Create a single page document", True),
        (1, "Generate 1 page of content", True),
        (2, "Write a two-page section", True),
        (2, "Create a 2-page document", True),
        (2, "Generate 2 pages of content", True),
        (4, "Create a four-page document", True),
        (4, "Generate 4 pages of content", True),
        (1, "Write a document with no page limit mentioned", False),
        (2, "Create content for 3 pages", False),  # Wrong number
    ]
    
    print("Testing validation patterns...")
    
    all_passed = True
    for expected_limit, prompt, should_pass in test_cases:
        # Create a mock TOC section
        toc_section = {
            "title": "Test Section",
            "page_limit": expected_limit,
            "description": "Test description"
        }
        
        # Create a mock outline
        outline = {
            "title": "Test Section",
            "content": "Test content that is long enough to pass validation checks",
            "page_limit": expected_limit,
            "purpose": "Test purpose",
            "rfp_vector_db_query": "test query",
            "client_vector_db_query": "test query",
            "custom_prompt": prompt,
            "references": "test references"
        }
        
        # Run validation
        errors = service._validate_outline_against_toc(outline, toc_section)
        
        # Check if page limit validation passed
        page_limit_error = any("Custom prompt must specify exactly" in error for error in errors)
        validation_passed = not page_limit_error
        
        if validation_passed == should_pass:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
            all_passed = False
        
        print(f"{status}: Expected {expected_limit} pages, prompt: '{prompt[:50]}...', should_pass: {should_pass}, actual_pass: {validation_passed}")
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 ALL VALIDATION PATTERN TESTS PASSED!")
        return True
    else:
        print("❌ SOME VALIDATION PATTERN TESTS FAILED!")
        return False

if __name__ == "__main__":
    success = test_validation_patterns()
    sys.exit(0 if success else 1)
