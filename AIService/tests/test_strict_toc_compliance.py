#!/usr/bin/env python3
"""
Test to verify outline generation STRICTLY follows table of contents
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from AIService
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent.parent / ".env")
except ImportError:
    print("python-dotenv not available. Please run from virtual environment.")
    sys.exit(1)

from services.proposal.outline import ProposalOutlineService
from services.proposal.structure_compliance import StructureComplianceService

try:
    from loguru import logger
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
except ImportError:
    print("loguru not available. Please run from virtual environment.")
    sys.exit(1)

async def test_strict_toc_compliance():
    """Test that outline generation STRICTLY follows table of contents"""
    
    # Initialize the services
    logger.info("Initializing services...")
    outline_service = ProposalOutlineService()
    structure_service = StructureComplianceService()
    
    # Test parameters
    test_opportunity_id = "vSe1unlCj9"
    test_tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    test_source = "custom"
    
    logger.info(f"Testing with opportunity_id={test_opportunity_id}")
    
    try:
        # Step 1: Generate structure compliance
        logger.info("Step 1: Generating structure compliance...")
        structure_result = await structure_service.generate_structure_compliance(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            max_tokens=3096
        )
        structure_compliance = structure_result["content"]
        logger.info("✅ Structure compliance generated")
        
        # Step 2: Generate table of contents with page limits
        logger.info("Step 2: Generating table of contents...")
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            volume_information=structure_compliance,
            content_compliance="",  # Not needed for this test
            is_rfp=True
        )
        
        # Parse table of contents
        toc_content = toc_result["content"]
        if toc_content.startswith("```json"):
            toc_content = toc_content.replace("```json", "").replace("```", "").strip()
        toc_data = json.loads(toc_content)
        table_of_contents = toc_data["table_of_contents"]
        
        logger.info(f"✅ Table of contents generated with {len(table_of_contents)} sections")
        
        # Print TOC for reference
        logger.info("Table of Contents:")
        for i, section in enumerate(table_of_contents):
            logger.info(f"  {i+1}. {section.get('title', '')} - {section.get('page_limit', 0)} pages")
        
        # Step 3: Generate outline using ONLY table of contents
        logger.info("Step 3: Generating outline with strict TOC compliance...")
        outline_result = await outline_service.generate_outline(
            opportunity_id=test_opportunity_id,
            tenant_id=test_tenant_id,
            source=test_source,
            table_of_contents=table_of_contents,
            is_rfp=True
        )
        
        logger.info("✅ Outline generated - checking strict compliance...")
        
        # Extract outline data
        outlines = outline_result.get("outlines", [])
        generation_summary = outline_result.get("generation_summary", {})
        
        logger.info(f"Generated {len(outlines)} outlines")
        logger.info(f"Success rate: {generation_summary.get('success_rate', 0):.1f}%")
        
        # STRICT COMPLIANCE VERIFICATION
        success = True
        compliance_errors = []
        
        # Check that we have the same number of outlines as TOC sections
        if len(outlines) != len(table_of_contents):
            error = f"MISMATCH: Expected {len(table_of_contents)} outlines, got {len(outlines)}"
            compliance_errors.append(error)
            logger.error(f"❌ {error}")
            success = False
        
        # Check each outline against its corresponding TOC section
        for i, outline in enumerate(outlines):
            if i >= len(table_of_contents):
                break
                
            toc_section = table_of_contents[i]
            expected_title = toc_section.get("title", "")
            expected_page_limit = toc_section.get("page_limit", 2)
            expected_description = toc_section.get("description", "")
            
            actual_title = outline.get("title", "")
            actual_page_limit = outline.get("page_limit", 0)
            
            # STRICT title check
            if actual_title != expected_title:
                error = f"TITLE MISMATCH: Expected '{expected_title}', got '{actual_title}'"
                compliance_errors.append(error)
                logger.error(f"❌ {error}")
                success = False
            else:
                logger.info(f"✅ TITLE CORRECT: '{actual_title}'")
            
            # STRICT page limit check
            if actual_page_limit != expected_page_limit:
                error = f"PAGE LIMIT MISMATCH: '{expected_title}' expected {expected_page_limit}, got {actual_page_limit}"
                compliance_errors.append(error)
                logger.error(f"❌ {error}")
                success = False
            else:
                logger.info(f"✅ PAGE LIMIT CORRECT: '{expected_title}' = {actual_page_limit} pages")
            
            # Check that content is substantial
            content_length = len(outline.get("content", ""))
            if content_length < 200:
                error = f"CONTENT TOO SHORT: '{expected_title}' content is only {content_length} characters"
                compliance_errors.append(error)
                logger.error(f"❌ {error}")
                success = False
            else:
                logger.info(f"✅ CONTENT ADEQUATE: '{expected_title}' content is {content_length} characters")
            
            # Check that custom prompt mentions correct page limit
            custom_prompt = outline.get("custom_prompt", "")
            if str(expected_page_limit) not in custom_prompt:
                error = f"CUSTOM PROMPT MISSING PAGE LIMIT: '{expected_title}' prompt doesn't mention {expected_page_limit} pages"
                compliance_errors.append(error)
                logger.error(f"❌ {error}")
                success = False
            else:
                logger.info(f"✅ CUSTOM PROMPT CORRECT: '{expected_title}' mentions {expected_page_limit} pages")
        
        # Save results for inspection
        output_dir = Path("test_outputs")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "strict_toc_compliance_test.json", "w") as f:
            json.dump({
                "outlines": outlines,
                "table_of_contents": table_of_contents,
                "generation_summary": generation_summary,
                "compliance_errors": compliance_errors,
                "strict_compliance_passed": success
            }, f, indent=2)
        
        logger.info(f"✅ Results saved to {output_dir}/strict_toc_compliance_test.json")
        
        if success:
            logger.success("🎉 STRICT TOC COMPLIANCE TEST PASSED!")
        else:
            logger.error("❌ STRICT TOC COMPLIANCE TEST FAILED!")
            logger.error("Compliance Errors:")
            for error in compliance_errors:
                logger.error(f"  - {error}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Error during strict TOC compliance testing: {e}")
        raise

def main():
    """Main function to run the strict TOC compliance test"""
    
    logger.info("Starting Strict TOC Compliance Test")
    logger.info("=" * 60)
    
    try:
        success = asyncio.run(test_strict_toc_compliance())
        
        if success:
            logger.success("🎉 All tests passed - Outline generation strictly follows TOC!")
            sys.exit(0)
        else:
            logger.error("❌ Tests failed - Outline generation does NOT follow TOC strictly!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
