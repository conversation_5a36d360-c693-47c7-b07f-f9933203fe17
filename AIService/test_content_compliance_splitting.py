#!/usr/bin/env python3
"""
Test script to verify content compliance splitting works correctly.
"""

import json
import os

def test_content_compliance_splitting():
    """Test the content compliance splitting function"""
    
    # Sample content compliance text (based on the actual generated content)
    content_compliance_text = """**RFP Compliance Checklist: Autonomous AI Recruiter Services**

**Volume I: Technical Capability (Page Limit: 10)**

**1.  General Requirements:**

*   The Technical Capability Volume shall be clear, concise, and include sufficient detail for effective evaluation and for substantiating the validity of stated claims in the Offeror's offer/quote.  Legibility, clarity and coherence are very important.
*   Offer/quotes will be evaluated against the Technical Capability factors defined in Addendum 52.212-2.
*   The offer/quote should not simply rephrase or restate the Government's requirements, but rather shall provide convincing rationale to address how the offeror intends to meet these requirements.

**2.  AI Engagement Services:**

*   Deliver autonomous, AI-driven outreach across SMS, phone calls, email, and chat from an AI recruiter specifically designed for higher education recruitment and trained on USMA's specific application processes, curriculum, and value propositions.
*   Deliver highly personalized engagements with students based on student inquiry/application data and interaction history.
*   Support both vendor-generated inquiries and imported leads from USMA's prospect, inquiry, and applicant pools.

**3.  Analytics and Reporting:**

*   Deliver real-time dashboards for tracking student engagement and completion rates.
*   Provide technical support for onboarding, training, and troubleshooting as needed.

**4.  Technical Integration:**

*   Ensure integration with USMA's existing Slate CRM.
*   Offer responsive technical support throughout the performance period, including initial implementation, issue resolution, and update deployment.

**Volume II: Price (Page Limit: N/A)**

**1. Pricing Requirements:**

*   The offeror shall complete Section B of the solicitation.  (Specific details of Section B are not provided in the context).
*   List Unit Pricing and Total Pricing of each Line Item -See Performance Work Statement (attached).
*   For the Price Volume, the electronic version will take precedence for any differences noted between the hard copy and electronic versions of an offeror's offer/quote.
*   Certified cost or pricing data is not anticipated due to expected competition.

**General Submission Requirements:**

*   Offers/Quotes shall be submitted electronically via email.
*   The offer/quote shall not exceed the limits stated above.
*   File naming convention: "Company Name – Initial" for the first Technical submission.
"""

    # Sample volume definitions (based on the actual structure compliance)
    volume_definitions = [
        {
            "volume_title": "Volume I - Technical Capability",
            "content": [
                {"section_name": "Executive Summary", "page_limit": 2},
                {"section_name": "Technical Approach", "page_limit": 4},
                {"section_name": "AI Recruiter Platform Description", "page_limit": 2},
                {"section_name": "Integration with USMA Systems", "page_limit": 1},
                {"section_name": "Predictive Data Model", "page_limit": 1}
            ]
        },
        {
            "volume_title": "Volume II - Price",
            "content": [
                {"section_name": "Pricing Details", "page_limit": 1}
            ]
        }
    ]

    def split_content_compliance_by_volume(content_compliance_text: str, volume_definitions: list) -> dict:
        """Split content compliance text into volume-specific portions"""
        volume_compliances = {}
        
        # Split the content compliance text into lines
        lines = content_compliance_text.split('\n')
        
        # Find volume sections in the content compliance
        volume_sections = {}
        current_volume = None
        current_content = []
        
        for line in lines:
            line_stripped = line.strip()
            line_lower = line_stripped.lower()
            
            # Check if this line starts a new volume section
            volume_found = None
            
            # Look for patterns like "**Volume I: Technical Capability**" or "Volume II: Price"
            for idx, vol_def in enumerate(volume_definitions, 1):
                vol_title = vol_def.get("volume_title", f"Volume {idx}")

                # Create specific patterns to match - be more precise
                roman_numerals = ['i', 'ii', 'iii', 'iv', 'v']
                patterns = [
                    f"**volume {idx}:",
                    f"**volume {roman_numerals[idx-1]}:",
                    f"volume {idx}:",
                    f"volume {roman_numerals[idx-1]}:"
                ]

                # Check if any pattern matches at the start of the line (more precise)
                if any(line_lower.startswith(pattern) or f"**{pattern}" in line_lower for pattern in patterns):
                    volume_found = idx
                    break
            
            if volume_found:
                # Save previous volume content if exists
                if current_volume is not None and current_content:
                    volume_sections[current_volume] = '\n'.join(current_content)
                
                # Start new volume
                current_volume = volume_found
                current_content = [line]
            elif current_volume is not None:
                # Add line to current volume
                current_content.append(line)
            else:
                # Before any volume is found, this is general content
                # We'll add it to all volumes later
                pass
        
        # Save the last volume
        if current_volume is not None and current_content:
            volume_sections[current_volume] = '\n'.join(current_content)
        
        # Create volume-specific compliance documents
        for idx, vol_def in enumerate(volume_definitions, 1):
            vol_title = vol_def.get("volume_title", f"Volume {idx}")
            
            # Start with volume-specific content if found
            if idx in volume_sections:
                volume_content = volume_sections[idx]
            else:
                # Fallback: create basic content for this volume
                volume_content = f"**{vol_title}**\n\nRequirements for {vol_title}:\n"
                
                # Add section-specific requirements based on volume definition
                vol_sections_def = vol_def.get("content", [])
                for section in vol_sections_def:
                    section_name = section.get("section_name", "")
                    page_limit = section.get("page_limit", "N/A")
                    volume_content += f"\n**{section_name}** (Page Limit: {page_limit})\n"
                    volume_content += f"- Provide detailed information for {section_name}\n"
            
            # Add general submission requirements that apply to all volumes
            general_requirements = []
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in [
                    "general submission", "format requirements", "submission instructions",
                    "file naming", "electronic submission", "deadline"
                ]) and "volume" not in line_lower:
                    general_requirements.append(line)
            
            if general_requirements:
                volume_content += "\n\n**General Requirements:**\n" + '\n'.join(general_requirements)
            
            volume_compliances[idx] = volume_content
        
        return volume_compliances

    # Test the splitting function
    print("="*80)
    print("TESTING CONTENT COMPLIANCE SPLITTING")
    print("="*80)
    
    split_compliances = split_content_compliance_by_volume(content_compliance_text, volume_definitions)
    
    for volume_num, compliance_content in split_compliances.items():
        vol_title = volume_definitions[volume_num - 1]["volume_title"]
        print(f"\n{'-'*60}")
        print(f"VOLUME {volume_num}: {vol_title}")
        print(f"{'-'*60}")
        print(f"Content length: {len(compliance_content)} characters")
        print(f"First 200 characters:")
        print(compliance_content[:200] + "..." if len(compliance_content) > 200 else compliance_content)
        
        # Check if volume-specific content is present
        if f"Volume {['I', 'II', 'III', 'IV', 'V'][volume_num-1]}" in compliance_content:
            print("✅ Contains volume-specific content")
        else:
            print("❌ Missing volume-specific content")
    
    print("\n" + "="*80)
    print("TESTING RESULTS")
    print("="*80)
    
    # Verify that Volume 1 contains technical content
    vol1_content = split_compliances[1].lower()
    if "ai engagement" in vol1_content and "technical capability" in vol1_content:
        print("✅ Volume 1 contains technical content")
    else:
        print("❌ Volume 1 missing technical content")
    
    # Verify that Volume 2 contains pricing content
    vol2_content = split_compliances[2].lower()
    if "pricing" in vol2_content and "price" in vol2_content:
        print("✅ Volume 2 contains pricing content")
    else:
        print("❌ Volume 2 missing pricing content")
    
    # Verify that volumes have different content
    if split_compliances[1] != split_compliances[2]:
        print("✅ Volumes have different content")
    else:
        print("❌ Volumes have identical content")
    
    print("="*80)

if __name__ == "__main__":
    test_content_compliance_splitting()
