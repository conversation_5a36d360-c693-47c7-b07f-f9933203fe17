#!/usr/bin/env python3
"""
Test script to verify the pipeline's content compliance splitting works correctly.
"""

import asyncio
import json
import os
from datetime import datetime

# Import the necessary modules
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.utilities import ProposalUtilities

async def test_pipeline_compliance_splitting():
    """Test the pipeline's content compliance splitting"""
    
    # Test parameters
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    
    print("="*80)
    print("TESTING PIPELINE CONTENT COMPLIANCE SPLITTING")
    print("="*80)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print("="*80)
    
    # Step 1: Generate content compliance
    print("Step 1: Generating content compliance...")
    content_service = ContentComplianceService()
    content_result = await content_service.generate_content_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        is_rfp=True
    )
    content_compliance = content_result.get("content", "")
    print(f"✅ Content compliance generated: {len(content_compliance)} characters")
    
    # Step 2: Generate structure compliance
    print("Step 2: Generating structure compliance...")
    structure_service = StructureComplianceService()
    structure_result = await structure_service.generate_structure_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source
    )
    structure_compliance = structure_result.get("content", "")
    print(f"✅ Structure compliance generated: {len(structure_compliance)} characters")
    
    # Step 3: Parse structure compliance
    print("Step 3: Parsing structure compliance...")
    structure_compliance_data = ProposalUtilities.extract_json_from_brackets(structure_compliance)
    if not structure_compliance_data or "structure" not in structure_compliance_data:
        print("❌ Error: Invalid structure compliance JSON")
        return
    
    volume_definitions = structure_compliance_data["structure"]
    print(f"✅ Found {len(volume_definitions)} volume definitions")
    
    for idx, vol_def in enumerate(volume_definitions, 1):
        vol_title = vol_def.get("volume_title", f"Volume {idx}")
        sections = vol_def.get("content", [])
        print(f"   Volume {idx}: {vol_title} ({len(sections)} sections)")
    
    # Step 4: Test content compliance splitting
    print("Step 4: Testing content compliance splitting...")
    
    def split_content_compliance_by_volume(content_compliance_text: str, volume_definitions: list) -> dict:
        """Split content compliance text into volume-specific portions"""
        volume_compliances = {}
        
        # Split the content compliance text into lines
        lines = content_compliance_text.split('\n')
        
        # Find volume sections in the content compliance
        volume_sections = {}
        current_volume = None
        current_content = []
        
        for line in lines:
            line_stripped = line.strip()
            line_lower = line_stripped.lower()
            
            # Check if this line starts a new volume section
            volume_found = None
            
            # Look for patterns like "**Volume I: Technical Capability**" or "Volume II: Price"
            for idx, vol_def in enumerate(volume_definitions, 1):
                vol_title = vol_def.get("volume_title", f"Volume {idx}")
                
                # Create specific patterns to match - be more precise
                roman_numerals = ['i', 'ii', 'iii', 'iv', 'v']
                patterns = [
                    f"**volume {idx}:",
                    f"**volume {roman_numerals[idx-1]}:",
                    f"volume {idx}:",
                    f"volume {roman_numerals[idx-1]}:"
                ]
                
                # Check if any pattern matches at the start of the line (more precise)
                if any(line_lower.startswith(pattern) or f"**{pattern}" in line_lower for pattern in patterns):
                    volume_found = idx
                    break
            
            if volume_found:
                # Save previous volume content if exists
                if current_volume is not None and current_content:
                    volume_sections[current_volume] = '\n'.join(current_content)
                
                # Start new volume
                current_volume = volume_found
                current_content = [line]
            elif current_volume is not None:
                # Add line to current volume
                current_content.append(line)
            else:
                # Before any volume is found, this is general content
                pass
        
        # Save the last volume
        if current_volume is not None and current_content:
            volume_sections[current_volume] = '\n'.join(current_content)
        
        # Create volume-specific compliance documents
        for idx, vol_def in enumerate(volume_definitions, 1):
            vol_title = vol_def.get("volume_title", f"Volume {idx}")
            
            # Start with volume-specific content if found
            if idx in volume_sections:
                volume_content = volume_sections[idx]
            else:
                # Fallback: create basic content for this volume
                volume_content = f"**{vol_title}**\n\nRequirements for {vol_title}:\n"
                
                # Add section-specific requirements based on volume definition
                vol_sections_def = vol_def.get("content", [])
                for section in vol_sections_def:
                    section_name = section.get("section_name", "")
                    page_limit = section.get("page_limit", "N/A")
                    volume_content += f"\n**{section_name}** (Page Limit: {page_limit})\n"
                    volume_content += f"- Provide detailed information for {section_name}\n"
            
            # Add general submission requirements that apply to all volumes
            general_requirements = []
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in [
                    "general submission", "format requirements", "submission instructions",
                    "file naming", "electronic submission", "deadline"
                ]) and "volume" not in line_lower:
                    general_requirements.append(line)
            
            if general_requirements:
                volume_content += "\n\n**General Requirements:**\n" + '\n'.join(general_requirements)
            
            volume_compliances[idx] = volume_content
        
        return volume_compliances
    
    # Split content compliance by volume
    split_compliances = split_content_compliance_by_volume(content_compliance, volume_definitions)
    
    print("Step 5: Analyzing split results...")
    for idx, volume_data in split_compliances.items():
        vol_title = volume_definitions[idx - 1]["volume_title"]
        compliance_length = len(volume_data)
        print(f"✅ {vol_title}: {compliance_length} characters")
        
        # Show preview
        preview = volume_data[:150] + "..." if len(volume_data) > 150 else volume_data
        print(f"   Preview: {preview}")
        print()
    
    # Step 6: Verify volumes have different content
    print("Step 6: Verification...")
    if len(split_compliances) >= 2:
        if split_compliances[1] != split_compliances[2]:
            print("✅ Volumes have different content")
        else:
            print("❌ Volumes have identical content")
    
    # Check for volume-specific keywords
    for idx, compliance_content in split_compliances.items():
        vol_title = volume_definitions[idx - 1]["volume_title"]
        content_lower = compliance_content.lower()
        
        if idx == 1:  # Technical volume
            if any(keyword in content_lower for keyword in ["technical", "ai engagement", "analytics"]):
                print(f"✅ Volume {idx} contains technical keywords")
            else:
                print(f"❌ Volume {idx} missing technical keywords")
        elif idx == 2:  # Price volume
            if any(keyword in content_lower for keyword in ["price", "pricing", "cost"]):
                print(f"✅ Volume {idx} contains pricing keywords")
            else:
                print(f"❌ Volume {idx} missing pricing keywords")
    
    print("\n" + "="*80)
    print("CONTENT COMPLIANCE SPLITTING TEST COMPLETED")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(test_pipeline_compliance_splitting())
