import asyncio
import os
import json
import shutil
from datetime import datetime
from typing import Optional
from controllers.customer.tenant_controller import TenantController
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db


def create_dedicated_folder(opportunity_id: str) -> str:
    """Create a dedicated folder for all generated content"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_name = f"proposal_generation_{opportunity_id}_{timestamp}"
    folder_path = os.path.join("generated-proposals", folder_name)

    # Create the directory structure
    os.makedirs(folder_path, exist_ok=True)
    os.makedirs(os.path.join(folder_path, "outlines"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "drafts"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "pdfs"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "compliances"), exist_ok=True)

    print(f"Created dedicated folder: {folder_path}")
    return folder_path


def save_draft_to_json(draft_data: dict, volume_number: int, folder_path: str, opportunity_id: str) -> str:
    """Save draft data to JSON file in dedicated folder"""
    filename = f"draft_volume_{volume_number}_{opportunity_id}.json"
    filepath = os.path.join(folder_path, "drafts", filename)

    with open(filepath, 'w') as f:
        json.dump(draft_data, f, indent=2)

    print(f"Saved draft for volume {volume_number} to {filepath}")
    return filepath


def save_outline_to_json(outline_data: dict, volume_number: int, folder_path: str, opportunity_id: str) -> str:
    """Save outline data to JSON file in dedicated folder"""
    filename = f"outline_volume_{volume_number}_{opportunity_id}.json"
    filepath = os.path.join(folder_path, "outlines", filename)

    with open(filepath, 'w') as f:
        json.dump(outline_data, f, indent=2)

    print(f"Saved outline for volume {volume_number} to {filepath}")
    return filepath


def save_compliance_to_files(content_compliance: str, structure_compliance: str, folder_path: str, opportunity_id: str) -> tuple[str, str]:
    """Save compliance data to files in dedicated folder"""
    # Create compliance directory
    compliance_dir = os.path.join(folder_path, "compliances")
    os.makedirs(compliance_dir, exist_ok=True)

    # Save content compliance
    content_filename = f"content_compliance_{opportunity_id}.txt"
    content_filepath = os.path.join(compliance_dir, content_filename)
    with open(content_filepath, 'w') as f:
        f.write(content_compliance)

    # Save structure compliance
    structure_filename = f"structure_compliance_{opportunity_id}.txt"
    structure_filepath = os.path.join(compliance_dir, structure_filename)
    with open(structure_filepath, 'w') as f:
        f.write(structure_compliance)

    print(f"Saved content compliance to {content_filepath}")
    print(f"Saved structure compliance to {structure_filepath}")

    return content_filepath, structure_filepath


async def export_outline_to_pdf_using_controller(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export outline to PDF using RfpDraftExportController"""
    try:
        # Convert outline data to draft format for the controller
        if "outlines" in outline_data:
            draft_list = []
            for i, section in enumerate(outline_data["outlines"], 1):
                draft_list.append({
                    "title": section.get("title", f"Section {i}"),
                    "content": section.get("content", "No content available")
                })
        else:
            draft_list = []

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Temporarily update the database with this outline as draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate PDF
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=None,
                trailing_page_id=None,
                volume_number=volume_number
            )
            break

        # Move the generated PDF to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            pdf_filename = f"outline_volume_{volume_number}_{opportunity_id}.pdf"
            target_path = os.path.join(folder_path, "pdfs", pdf_filename)

            # Create the PDF directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported outline for volume {volume_number} to PDF: {target_path}")
            return target_path
        else:
            print(f"Error: Outline PDF generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting outline to PDF: {e}")
        return ""


async def export_draft_to_pdf_using_controller(
    draft_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export draft to PDF using RfpDraftExportController exactly like the export endpoint"""
    try:
        # First, we need to temporarily save the draft data to the database
        # so the controller can read it (since it reads from CustomOppsTable.draft)

        # Save the draft data to a temporary database entry or use the existing one
        # For now, we'll create a temporary JSON file that mimics the database structure
        temp_draft_file = os.path.join(folder_path, "drafts", f"temp_draft_vol_{volume_number}.json")

        # Convert draft_data to the format expected by the controller
        if "draft" in draft_data:
            draft_list = draft_data["draft"]
        else:
            draft_list = []

        # Save temporary draft file
        with open(temp_draft_file, 'w') as f:
            json.dump(draft_list, f, indent=2)

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Temporarily update the database with this volume's draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate PDF exactly like the export endpoint
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=None,  # No cover page for individual volumes
                trailing_page_id=None,  # No trailing page for individual volumes
                volume_number=volume_number
            )
            break

        # Move the generated PDF to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            pdf_filename = f"draft_volume_{volume_number}_{opportunity_id}.pdf"
            target_path = os.path.join(folder_path, "pdfs", pdf_filename)

            # Create the PDF directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported draft for volume {volume_number} to PDF using controller: {target_path}")
            return target_path
        else:
            print(f"Error: PDF generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting draft to PDF using controller: {e}")
        return ""


async def main():
    """
    Enhanced pipeline that saves data to CustomOppsTable and generates separate volume files.
    """

    # Configuration
    # opportunity_id = "iRiYNgd8RC"
    # opportunity_id = "GCP603BfMN"
    opportunity_id = "vSe1unlCj9"

    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"

    print("="*80)
    print("ENHANCED PIPELINE 6: CUSTOMOPPSTABLE INTEGRATION")
    print("="*80)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client: {client}")
    print("="*80)

    # Create dedicated folder for all generated content
    folder_path = create_dedicated_folder(opportunity_id)

    # Initialize services
    outline_service = ProposalOutlineService()
    content_compliance_service = ContentComplianceService()
    structure_compliance_service = StructureComplianceService()
    custom_controller = CustomOpportunitiesController
    tenant_controller = TenantController

    # Get tenant metadata
    tenant_metadata = ""
    async for db in get_customer_db():
        tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}"
        break

    # Generate content compliance and structure compliance
    print("Generating content compliance...")
    content_compliance_result = await content_compliance_service.generate_content_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        is_rfp=True
    )
    content_compliance = content_compliance_result.get("content", "")
    structured_content_data = content_compliance_result.get("structured_data", None)

    print("Generating structure compliance...")
    structure_compliance_result = await structure_compliance_service.generate_structure_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source
    )
    structure_compliance = structure_compliance_result.get("content", "")

    # Save compliance data to files
    print("Saving compliance data to files...")
    save_compliance_to_files(content_compliance, structure_compliance, folder_path, opportunity_id)
    


    # Parse structure compliance to get actual volume definitions
    print("Parsing structure compliance to determine volumes...")
    structure_compliance_data = ProposalUtilities.extract_json_from_brackets(structure_compliance)
    if not structure_compliance_data or "structure" not in structure_compliance_data:
        print("Error: Invalid structure compliance JSON, cannot determine volumes")
        return

    volume_definitions = structure_compliance_data["structure"]
    print(f"Found {len(volume_definitions)} volume definitions in structure compliance")

    # Step 1: Process content compliance by volume (structured or split)
    print("Processing content compliance by volume...")
    volume_content_compliances = {}

    # Check if we have structured content compliance data
    if structured_content_data and "content_compliance" in structured_content_data:
        print("✅ Using structured content compliance data from LLM...")
        structured_volumes = structured_content_data["content_compliance"]

        # Map structured content to volumes
        for idx, volume_def in enumerate(volume_definitions, 1):
            volume_title = volume_def.get("volume_title", f"Volume {idx}")

            # Find matching structured content
            matching_content = ""
            for structured_vol in structured_volumes:
                struct_title = structured_vol.get("volume_title", "")
                if any(identifier in struct_title.lower() for identifier in [
                    f"volume {idx}", f"volume {['i', 'ii', 'iii', 'iv', 'v'][idx-1]}",
                    volume_title.lower()
                ]):
                    matching_content = structured_vol.get("content", "")
                    break

            volume_content_compliances[idx] = {
                "volume_title": volume_title,
                "volume_definition": volume_def,
                "content_compliance": matching_content
            }

            # Save volume-specific content compliance file
            volume_compliance_path = os.path.join(folder_path, "compliances", f"content_compliance_volume_{idx}.txt")
            with open(volume_compliance_path, 'w') as f:
                f.write(f"Content Compliance for {volume_title}\n")
                f.write("="*50 + "\n\n")
                f.write(matching_content)
            print(f"✅ Structured content compliance for {volume_title}: {len(matching_content)} characters")
    else:
        print("⚠️  Falling back to text-based content compliance splitting...")

    def split_content_compliance_by_volume(content_compliance_text: str, volume_definitions: list) -> dict:
        """Split content compliance text into volume-specific portions"""
        volume_compliances = {}

        # Split the content compliance text into sections
        lines = content_compliance_text.split('\n')
        current_volume = None
        volume_content = {}

        for line in lines:
            line_lower = line.lower().strip()

            # Check if this line indicates a new volume
            volume_found = None
            for idx, vol_def in enumerate(volume_definitions, 1):
                vol_title = vol_def.get("volume_title", f"Volume {idx}").lower()

                # Look for volume indicators in the line
                if any(indicator in line_lower for indicator in [
                    vol_title.lower(),
                    f"volume {idx}",
                    f"vol {idx}",
                    f"volume i" if idx == 1 else f"volume {'i' * idx}",
                ]):
                    volume_found = idx
                    break

            if volume_found:
                # Save previous volume content if exists
                if current_volume and current_volume in volume_content:
                    volume_compliances[current_volume] = '\n'.join(volume_content[current_volume])

                # Start new volume
                current_volume = volume_found
                if current_volume not in volume_content:
                    volume_content[current_volume] = []
                volume_content[current_volume].append(line)
            elif current_volume:
                # Add line to current volume
                volume_content[current_volume].append(line)
            else:
                # No volume identified yet, add to all volumes (general requirements)
                for idx in range(1, len(volume_definitions) + 1):
                    if idx not in volume_content:
                        volume_content[idx] = []
                    volume_content[idx].append(line)

        # Save the last volume
        if current_volume and current_volume in volume_content:
            volume_compliances[current_volume] = '\n'.join(volume_content[current_volume])

        # If no volume-specific content was found, split by sections mentioned in volume definitions
        if not volume_compliances:
            for idx, vol_def in enumerate(volume_definitions, 1):
                vol_title = vol_def.get("volume_title", f"Volume {idx}")
                vol_sections = vol_def.get("content", [])

                # Extract content related to this volume's sections
                volume_specific_content = []
                volume_specific_content.append(f"**{vol_title} Requirements:**\n")

                for section in vol_sections:
                    section_name = section.get("section_name", "")
                    # Look for content related to this section
                    for line in lines:
                        if any(keyword.lower() in line.lower() for keyword in [
                            section_name.lower(),
                            section_name.replace(" ", "").lower(),
                        ]):
                            volume_specific_content.append(line)

                # Add general requirements that apply to this volume
                general_requirements = []
                for line in lines:
                    line_lower = line.lower()
                    if any(keyword in line_lower for keyword in [
                        "page limit", "format", "font", "margin", "submission",
                        "deadline", "evaluation", "criteria", "requirement"
                    ]):
                        general_requirements.append(line)

                volume_specific_content.extend(general_requirements)
                volume_compliances[idx] = '\n'.join(volume_specific_content)

        return volume_compliances

    # Split content compliance by volume
    split_compliances = split_content_compliance_by_volume(content_compliance, volume_definitions)

    for idx, volume_definition in enumerate(volume_definitions, 1):
        volume_title = volume_definition.get("volume_title", f"Volume {idx}")

        # Get volume-specific content compliance
        volume_specific_compliance = split_compliances.get(idx, content_compliance)

        volume_content_compliances[idx] = {
            "volume_title": volume_title,
            "volume_definition": volume_definition,
            "content_compliance": volume_specific_compliance
        }

        compliance_length = len(volume_specific_compliance)
        print(f"Split content compliance for {volume_title}: {compliance_length} characters")

        # Save volume-specific content compliance to file for verification
        volume_compliance_file = os.path.join(folder_path, "compliances", f"content_compliance_volume_{idx}.txt")
        with open(volume_compliance_file, 'w') as f:
            f.write(f"Content Compliance for {volume_title}\n")
            f.write("="*50 + "\n\n")
            f.write(volume_specific_compliance)

        # Log a preview of the volume-specific compliance
        preview = volume_specific_compliance[:200] + "..." if len(volume_specific_compliance) > 200 else volume_specific_compliance
        print(f"  Preview: {preview}")
        print(f"  Saved to: {volume_compliance_file}")

    # Step 2: Generate TOC for each volume using its specific content compliance
    print("Generating table of contents for each volume...")
    volumes = {}

    for idx, volume_data in volume_content_compliances.items():
        volume_title = volume_data["volume_title"]
        volume_definition = volume_data["volume_definition"]
        volume_content_compliance = volume_data["content_compliance"]

        print(f"Generating TOC for {volume_title}...")

        # Generate table of contents for this specific volume with its content compliance
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            volume_information=json.dumps(volume_definition),  # Pass individual volume definition
            content_compliance=volume_content_compliance,  # Use volume-specific content compliance
            is_rfp=True
        )

        # Extract table of contents for this volume
        toc_content = toc_result.get("content", "")
        if toc_content:
            try:
                table_of_contents_data = ProposalUtilities.extract_json_from_brackets(toc_content)
                volume_toc = table_of_contents_data.get("table_of_contents", []) if table_of_contents_data else []
            except Exception as e:
                print(f"Error parsing table of contents for {volume_title}: {e}")
                volume_toc = []
        else:
            volume_toc = []

        volumes[idx] = {
            "volume_title": volume_title,
            "volume_definition": volume_definition,
            "table_of_contents": volume_toc,
            "content_compliance": volume_content_compliance
        }

        print(f"Generated {len(volume_toc)} sections for {volume_title}")

    print(f"Successfully generated TOCs for {len(volumes)} volumes")

    # Step 3: Generate outlines for each volume using its specific TOC
    print("Generating outlines for each volume...")
    all_outlines = {}
    all_drafts = {}
    total_sections = 0

    for volume_number, volume_data in volumes.items():
        volume_title = volume_data["volume_title"]
        volume_toc = volume_data["table_of_contents"]
        total_sections += len(volume_toc)

        print(f"Step 3.{volume_number}: Generating outline for {volume_title} with {len(volume_toc)} sections...")

        # Generate outline for this volume using ONLY its specific TOC
        outline_result = await outline_service.generate_outline(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=volume_toc  # Use only this volume's specific TOC
        )

        # Save outline to JSON in dedicated folder
        outline_json_path = save_outline_to_json(outline_result, volume_number, folder_path, opportunity_id)
        all_outlines[volume_number] = outline_result
        print(f"  ✅ Outline generated and saved for {volume_title}")

    # Step 4: Generate drafts for each volume using its specific TOC
    print("Generating drafts for each volume...")

    for volume_number, volume_data in volumes.items():
        volume_title = volume_data["volume_title"]
        volume_toc = volume_data["table_of_contents"]

        print(f"Step 4.{volume_number}: Generating draft for {volume_title}...")

        # Generate draft for this volume using ONLY its specific TOC
        draft_result = await outline_service.generate_draft(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=volume_toc,  # Use only this volume's specific TOC
            tenant_metadata=tenant_metadata,
            client_short_name=client
        )

        # Save draft to JSON in dedicated folder
        draft_json_path = save_draft_to_json(draft_result, volume_number, folder_path, opportunity_id)
        all_drafts[volume_number] = draft_result
        print(f"  ✅ Draft generated and saved for {volume_title}")

    # Step 5: Export each volume to PDF with its specific TOC
    print("Exporting each volume to PDF with volume-specific TOCs...")

    for volume_number, volume_data in volumes.items():
        volume_title = volume_data["volume_title"]
        volume_toc = volume_data["table_of_contents"]
        outline_result = all_outlines[volume_number]
        draft_result = all_drafts[volume_number]

        print(f"Step 5.{volume_number}: Exporting {volume_title} to PDF...")

        # Export outline to PDF with this volume's specific TOC
        print(f"  Exporting outline for {volume_title} to PDF...")
        outline_pdf_path = await export_outline_to_pdf_using_controller(
            outline_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
        )

        # Export draft to PDF with this volume's specific TOC
        print(f"  Exporting draft for {volume_title} to PDF...")
        draft_pdf_path = await export_draft_to_pdf_using_controller(
            draft_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
        )

        print(f"  ✅ {volume_title} PDF export completed:")
        print(f"     - Outline PDF: {outline_pdf_path}")
        print(f"     - Draft PDF: {draft_pdf_path}")
        print(f"     - TOC sections: {len(volume_toc)}")

    # Update CustomOppsTable with requirements, compliances, and each TOC/outline
    print("Updating CustomOppsTable with generated content...")

    # Prepare database update fields
    update_fields = {
        "content_compliance": content_compliance,
        "structure_compliance": structure_compliance,
        # Note: Ignoring the single 'draft' column as requested
    }

    # Save TOCs for each volume (up to 5 volumes supported by table structure)
    for volume_number, volume_data in volumes.items():
        if volume_number <= 5:
            toc_field = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
            update_fields[toc_field] = json.dumps(volume_data["table_of_contents"])

    # Save outlines for each volume (up to 5 volumes supported by table structure)
    for volume_number, outline_data in all_outlines.items():
        if volume_number <= 5:
            outline_field = f"proposal_outline_{volume_number}"
            update_fields[outline_field] = json.dumps(outline_data)

    # Update the database
    async for db in get_customer_db():
        await custom_controller.update_by_opportunity_id(db, opportunity_id, update_fields)
        break

    # Save summary files in dedicated folder
    summary_data = {
        "opportunity_id": opportunity_id,
        "tenant_id": tenant_id,
        "client": client,
        "generated_at": datetime.now().isoformat(),
        "volumes_count": len(volumes),
        "total_sections": total_sections,
        "content_compliance": content_compliance,
        "structure_compliance": structure_compliance,
        "volumes": {str(k): v for k, v in volumes.items()},
        "volume_content_compliances": {str(k): v["content_compliance"] for k, v in volume_content_compliances.items()},
        "folder_path": folder_path
    }

    summary_path = os.path.join(folder_path, "generation_summary.json")
    with open(summary_path, 'w') as f:
        json.dump(summary_data, f, indent=2)

    print("="*80)
    print("ENHANCED PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("CORRECT FLOW IMPLEMENTED:")
    print("1. ✅ Split content compliance by volume (intelligent parsing)")
    print("2. ✅ Generated volume-specific TOCs using volume-specific content compliance")
    print("3. ✅ Generated volume-specific outlines using volume-specific TOCs")
    print("4. ✅ Generated volume-specific drafts using volume-specific TOCs")
    print("5. ✅ Exported PDFs with volume-specific TOCs")
    print("="*80)
    print(f"Generated {len(volumes)} SEPARATE volumes from {total_sections} sections")
    print(f"All content saved to: {folder_path}")
    print(f"Database updated with:")
    print(f"   - Content compliance")
    print(f"   - Structure compliance")
    print(f"   - {len(volumes)} volume-specific TOCs (toc_text, toc_text_2, etc.)")
    print(f"   - {len(all_outlines)} volume-specific outlines (proposal_outline_1, proposal_outline_2, etc.)")
    print(f"   - Draft column IGNORED as requested")
    print(f"Generated files:")
    print(f"   - 2 compliance files (content & structure)")
    print(f"   - {len(volume_content_compliances)} volume-specific content compliance files")
    print(f"   - {len(all_outlines)} outline JSON files")
    print(f"   - {len(all_outlines)} outline PDF files (with volume-specific TOCs)")
    print(f"   - {len(all_drafts)} draft JSON files")
    print(f"   - {len(all_drafts)} draft PDF files (with volume-specific TOCs)")
    print(f"   - 1 generation summary file")
    print("="*80)
    print("KEY IMPROVEMENTS:")
    print("✅ Content compliance intelligently split by volume")
    print("✅ Each volume has its own unique content compliance portion")
    print("✅ Each volume has its own unique table of contents")
    print("✅ PDF exports use the correct TOC for each volume")
    print("✅ No more shared/duplicate TOCs between volumes")
    print("✅ Proper volume separation throughout the entire pipeline")
    print("✅ Volume-specific content compliance files saved for verification")
    print("="*80)
    
    


if __name__ == "__main__":
    asyncio.run(main())