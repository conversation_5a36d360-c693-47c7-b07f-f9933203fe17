#!/usr/bin/env python3
"""
Test script to verify the updated ContentComplianceService generates structured JSON output.
"""

import asyncio
import json
import os
import sys

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_json_extraction():
    """Test JSON extraction from sample LLM output"""
    
    # Sample LLM output with JSON
    sample_output = '''Based on the RFP documentation, here are the content compliance requirements:

```json
{
  "content_compliance": [
    {
      "volume_title": "Volume I",
      "content": "**Volume I: Technical Capability (Page Limit: 10)**\\n\\n**1. General Requirements:**\\n- The Technical Capability Volume shall be clear, concise, and include sufficient detail for effective evaluation\\n- Offer/quotes will be evaluated against the Technical Capability factors defined in Addendum 52.212-2\\n\\n**2. AI Engagement Services:**\\n- Deliver autonomous, AI-driven outreach across SMS, phone calls, email, and chat\\n- Support both vendor-generated inquiries and imported leads from USMA's prospect pools"
    },
    {
      "volume_title": "Volume II",
      "content": "**Volume II: Price (Page Limit: N/A)**\\n\\n**1. Pricing Requirements:**\\n- The offeror shall complete Section B of the solicitation\\n- List Unit Pricing and Total Pricing of each Line Item\\n- For the Price Volume, the electronic version will take precedence"
    }
  ]
}
```

This provides the structured content compliance requirements for each volume.'''

    print("="*80)
    print("TESTING JSON EXTRACTION FROM LLM OUTPUT")
    print("="*80)
    
    # Test the extraction function
    def extract_json_from_brackets(text):
        """Extract JSON content from text between brackets"""
        start_marker = "```json"
        end_marker = "```"

        start_index = text.find(start_marker)
        if start_index == -1:
            return None

        start_index += len(start_marker)
        end_index = text.find(end_marker, start_index)

        if end_index == -1:
            return None

        json_content = text[start_index:end_index].strip()

        # JSON content should already be properly formatted

        try:
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Problematic JSON content: {json_content[:200]}...")
            return None
    
    # Test extraction
    extracted_data = extract_json_from_brackets(sample_output)
    
    if extracted_data:
        print("✅ Successfully extracted JSON data")
        print(f"Keys: {list(extracted_data.keys())}")
        
        if "content_compliance" in extracted_data:
            volumes = extracted_data["content_compliance"]
            print(f"✅ Found {len(volumes)} volumes in content compliance")
            
            for i, volume in enumerate(volumes, 1):
                vol_title = volume.get("volume_title", f"Volume {i}")
                content = volume.get("content", "")
                print(f"\n{'-'*50}")
                print(f"Volume {i}: {vol_title}")
                print(f"Content length: {len(content)} characters")
                print(f"Preview: {content[:100]}...")
                
                # Check for volume-specific content
                content_lower = content.lower()
                if i == 1 and any(keyword in content_lower for keyword in ["technical", "ai engagement"]):
                    print("✅ Volume 1 contains technical content")
                elif i == 2 and any(keyword in content_lower for keyword in ["price", "pricing"]):
                    print("✅ Volume 2 contains pricing content")
        else:
            print("❌ No 'content_compliance' key found in extracted data")
    else:
        print("❌ Failed to extract JSON data")
    
    print("\n" + "="*80)
    print("JSON EXTRACTION TEST COMPLETED")
    print("="*80)

async def test_content_compliance_service():
    """Test the actual ContentComplianceService (if dependencies are available)"""
    
    print("\n" + "="*80)
    print("TESTING CONTENT COMPLIANCE SERVICE")
    print("="*80)
    
    try:
        from services.proposal.content_compliance import ContentComplianceService
        
        # Test parameters
        opportunity_id = "vSe1unlCj9"
        tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
        source = "custom"
        
        print(f"Opportunity ID: {opportunity_id}")
        print(f"Tenant ID: {tenant_id}")
        print(f"Source: {source}")
        
        # Create service instance
        content_service = ContentComplianceService()
        
        # Generate content compliance
        print("\nGenerating content compliance...")
        result = await content_service.generate_content_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            is_rfp=True
        )
        
        content = result.get("content", "")
        structured_data = result.get("structured_data", None)
        
        print(f"✅ Content compliance generated: {len(content)} characters")
        
        if structured_data:
            print("✅ Structured data available!")
            print(f"Structured data keys: {list(structured_data.keys())}")
            
            if "content_compliance" in structured_data:
                volumes = structured_data["content_compliance"]
                print(f"✅ Found {len(volumes)} volumes in structured data")
                
                for i, volume in enumerate(volumes, 1):
                    vol_title = volume.get("volume_title", f"Volume {i}")
                    vol_content = volume.get("content", "")
                    print(f"\nVolume {i}: {vol_title}")
                    print(f"Content length: {len(vol_content)} characters")
                    print(f"Preview: {vol_content[:100]}...")
            else:
                print("❌ No 'content_compliance' key in structured data")
        else:
            print("⚠️  No structured data returned, using text format")
            print(f"Text content preview: {content[:200]}...")
        
    except ImportError as e:
        print(f"⚠️  Cannot test ContentComplianceService: {e}")
        print("This is expected if dependencies are not installed")
    except Exception as e:
        print(f"❌ Error testing ContentComplianceService: {e}")
    
    print("\n" + "="*80)
    print("CONTENT COMPLIANCE SERVICE TEST COMPLETED")
    print("="*80)

def main():
    """Run all tests"""
    print("TESTING STRUCTURED CONTENT COMPLIANCE")
    print("="*80)
    
    # Test 1: JSON extraction
    test_json_extraction()
    
    # Test 2: Content compliance service (if available)
    try:
        asyncio.run(test_content_compliance_service())
    except Exception as e:
        print(f"Skipping service test due to: {e}")

if __name__ == "__main__":
    main()
